package common

import (
	"gitlab.lilithgame.com/yunwei/oma-infra/asset-syncer/internal/restclient/fields"
)

// 这个文件展示了如何为不同类型的资源使用不同的 Beauty 函数

// Example: 同步 VPC（需要区域字段）
func ExampleSyncVPC(s SyncerInterface, vpc any, beautyFunc func(any) fields.Fields, regionID int) fields.Fields {
	// VPC 需要工厂、账户和区域字段
	return BeautyWithSyncer(vpc, beautyFunc, s, regionID)
}

// Example: 同步 EIP（需要区域字段）
func ExampleSyncEIP(s SyncerInterface, eip any, beautyFunc func(any) fields.Fields, regionID int) fields.Fields {
	// EIP 需要工厂、账户和区域字段
	return BeautyWithSyncer(eip, beautyFunc, s, regionID)
}

// Example: 同步安全组（需要区域字段）
func ExampleSyncSecurityGroup(s SyncerInterface, sg any, beautyFunc func(any) fields.Fields, regionID int) fields.Fields {
	// 安全组需要工厂、账户和区域字段
	return BeautyWithSyncer(sg, beautyFunc, s, regionID)
}

// Example: 同步安全组规则（不需要区域字段）
func ExampleSyncSecurityGroupRule(rule any, beautyFunc func(any) fields.Fields, securityGroupID int) fields.Fields {
	// 安全组规则只需要关联到父安全组，不需要工厂、账户或区域字段
	return beautyFunc(rule).With(
		fields.NamedField("security", securityGroupID),
	)
}

// Example: 同步云主机（需要区域和可用区字段）
func ExampleSyncInstance(s SyncerInterface, instance any, beautyFunc func(any) fields.Fields, regionID int, zoneID int) fields.Fields {
	// 云主机需要工厂、账户、区域和可用区字段
	return BeautyWithZone(instance, beautyFunc, s, regionID, zoneID)
}

// Example: 同步全局资源（只需要工厂和账户字段）
func ExampleSyncGlobalResource(s SyncerInterface, resource any, beautyFunc func(any) fields.Fields) fields.Fields {
	// 全局资源（如 CDN、DNS 等）只需要工厂和账户字段
	return BeautyWithSyncerOnly(resource, beautyFunc, s)
}

// Example: 同步子资源（不需要任何额外字段）
func ExampleSyncSubResource(resource any, beautyFunc func(any) fields.Fields, parentID int, parentField string) fields.Fields {
	// 子资源只需要关联到父资源
	return beautyFunc(resource).With(
		fields.NamedField(parentField, parentID),
	)
}

/*
使用指南：

1. **区域级资源**（VPC、EIP、安全组等）：
   使用 BeautyWithSyncer(obj, beautyFunc, syncer, regionID)

2. **可用区级资源**（云主机、磁盘等）：
   使用 BeautyWithZone(obj, beautyFunc, syncer, regionID, zoneID)

3. **全局资源**（CDN、DNS、IAM 等）：
   使用 BeautyWithSyncerOnly(obj, beautyFunc, syncer)

4. **子资源**（安全组规则、负载均衡监听器等）：
   直接使用 beautyFunc(obj).With(parentFields...)

5. **特殊情况**：
   如果需要自定义字段组合，可以直接使用 WithSyncerFields 等工具函数
*/
